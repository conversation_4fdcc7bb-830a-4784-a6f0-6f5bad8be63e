const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');


const TempUserSchema = new mongoose.Schema(
  {
    firstName: {
      type: String,
      required: [true, 'Please add a first name'],
      trim: true,
      maxlength: [50, 'First name cannot be more than 50 characters']
    },
    lastName: {
      type: String,
      required: [true, 'Please add a last name'],
      trim: true,
      maxlength: [50, 'Last name cannot be more than 50 characters']
    },
    email: {
      type: String,
      required: [true, 'Please add an email'],
      unique: true,
      match: [
        /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
        'Please add a valid email'
      ]
    },
    mobile: {
      type: String,
      required: [true, 'Please add a mobile number'],
      maxlength: [20, 'Mobile number cannot be longer than 20 characters'],
      validate: {
        validator: function (value) {
          // Skip validation for empty values (handled by required)
          if (!value) return true;

          // Basic mobile number validation
          return /^\d{10,15}$/.test(value.replace(/\D/g, ''));
        },
        message: 'Please enter a valid mobile number'
      }
    },
    role: {
      type: String,
      enum: ['buyer', 'seller', 'admin'],
      default: 'buyer'
    },
    // Active role for dual buyer-seller users (admin users don't have activeRole)
    activeRole: {
      type: String,
      enum: ['buyer', 'seller'],
      default: function () {
        // Set activeRole to match role for non-admin users
        return this.role === 'admin' ? undefined : this.role;
      },
    },
    password: {
      type: String,
      required: [true, 'Please add a password'],
      minlength: [6, 'Password must be at least 6 characters'],
      select: false, // Don't include password in queries by default
    },
    createdAt: {
      type: Date,
      default: Date.now,
      expires: 3600 // Document expires after 1 hour
    }
  },
  {
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Encrypt password using bcrypt
TempUserSchema.pre('save', async function (next) {
  if (!this.isModified('password')) {
    next();
  }

  const salt = await bcrypt.genSalt(10);
  this.password = await bcrypt.hash(this.password, salt);
});

module.exports = mongoose.model('TempUser', TempUserSchema);
