/**
 * Email templates for various notifications
 */

/**
 * Standard date formatter for consistent display across emails
 * Format: "Jul 4, 2025, 04:15 PM"
 * @param {string|Date} date - The date to format
 * @returns {string} - Formatted date string in standard format
 */
const formatStandardDate = (date) => {
  if (!date) return 'N/A';

  try {
    const dateObj = new Date(date);

    // Check if date is valid
    if (isNaN(dateObj.getTime())) {
      console.warn('Invalid date provided to formatStandardDate:', date);
      return 'Invalid Date';
    }

    return dateObj.toLocaleDateString('en-US', {
      month: 'short',    // Jul
      day: 'numeric',    // 4
      year: 'numeric',   // 2025
      hour: '2-digit',   // 04
      minute: '2-digit', // 15
      hour12: true       // PM
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid Date';
  }
};

/**
 * Generate auction bid acceptance email template
 * @param {Object} data - Email data
 * @param {Object} data.bidder - Bidder information
 * @param {Object} data.content - Content information
 * @param {Object} data.bid - Bid information
 * @param {string} data.checkoutUrl - Checkout URL
 * @returns {Object} Email template with subject, text, and html
 */
const generateBidAcceptanceEmail = (data) => {
  const { bidder, content, bid, checkoutUrl } = data;

  // Get payment timeout from configuration
  const { PAYMENT_DEADLINE_HOURS } = require('../config/timeouts');
  const timeoutText = PAYMENT_DEADLINE_HOURS >= 1
    ? `${PAYMENT_DEADLINE_HOURS} hour${PAYMENT_DEADLINE_HOURS > 1 ? 's' : ''}`
    : `${Math.round(PAYMENT_DEADLINE_HOURS * 60)} minutes`;

  const subject = `🎉 Congratulations! Your bid has been accepted - ${content.title}`;

  const textMessage = `
Congratulations ${bidder.firstName}!

Your bid of $${bid.amount.toFixed(2)} for "${content.title
    }" has been accepted by the seller.

Content Details:
- Title: ${content.title}
- Sport: ${content.sport}
- Content Type: ${content.contentType}
- Seller: ${content.seller.firstName} ${content.seller.lastName}

Your winning bid: $${bid.amount.toFixed(2)}

To complete your purchase and access the content, please click the link below:
${checkoutUrl}

This link will take you to our secure checkout page where you can complete your payment.

Important Notes:
- You have ${timeoutText} to complete the payment
- After successful payment, you'll have immediate access to preview the content
- If you have any questions, please contact our support team

Thank you for using XO Sports Hub!

Best regards,
The XO Sports Hub Team
  `;

  const htmlMessage = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f9f9f9; padding: 20px;">
      <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <!-- Header -->
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #2c5aa0; margin: 0; font-size: 28px;">🎉 Congratulations!</h1>
          <p style="color: #666; font-size: 18px; margin: 10px 0 0 0;">Your bid has been accepted</p>
        </div>

        <!-- Greeting -->
        <div style="margin-bottom: 25px;">
          <p style="font-size: 16px; color: #333; margin: 0;">Dear ${bidder.firstName
    },</p>
        </div>

        <!-- Main Message -->
        <div style="background-color: #e8f4fd; padding: 20px; border-radius: 8px; margin-bottom: 25px; border-left: 4px solid #2c5aa0;">
          <p style="font-size: 16px; color: #333; margin: 0 0 10px 0;">
            Great news! Your bid of <strong style="color: #2c5aa0;">$${bid.amount.toFixed(
      2
    )}</strong> for 
            "<strong>${content.title}</strong>" has been accepted by the seller.
          </p>
        </div>

        <!-- Content Details -->
        <div style="margin-bottom: 25px;">
          <h3 style="color: #2c5aa0; margin: 0 0 15px 0; font-size: 18px;">Content Details</h3>
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="padding: 8px 0; color: #666; font-weight: bold; width: 30%;">Title:</td>
              <td style="padding: 8px 0; color: #333;">${content.title}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #666; font-weight: bold;">Sport:</td>
              <td style="padding: 8px 0; color: #333;">${content.sport}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #666; font-weight: bold;">Content Type:</td>
              <td style="padding: 8px 0; color: #333;">${content.contentType
    }</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #666; font-weight: bold;">Seller:</td>
              <td style="padding: 8px 0; color: #333;">${content.seller.firstName
    } ${content.seller.lastName}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #666; font-weight: bold;">Your Winning Bid:</td>
              <td style="padding: 8px 0; color: #2c5aa0; font-weight: bold; font-size: 18px;">$${bid.amount.toFixed(
      2
    )}</td>
            </tr>
          </table>
        </div>

        <!-- CTA Button -->
        <div style="text-align: center; margin: 30px 0;">
          <a href="${checkoutUrl}" 
             style="background-color: #2c5aa0; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; font-size: 16px; display: inline-block;">
            Complete Your Purchase
          </a>
        </div>

        <!-- Important Notes -->
        <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107; margin-bottom: 25px;">
          <h4 style="color: #856404; margin: 0 0 10px 0; font-size: 16px;">Important Notes:</h4>
          <ul style="color: #856404; margin: 0; padding-left: 20px;">
            <li>You have ${timeoutText} to complete the payment</li>
            <li>After successful payment, you'll have immediate access to preview the content</li>
            <li>If you have any questions, please contact our support team</li>
          </ul>
        </div>

        <!-- Footer -->
        <div style="text-align: center; padding-top: 20px; border-top: 1px solid #eee;">
          <p style="color: #666; font-size: 14px; margin: 0;">
            Thank you for using XO Sports Hub!<br>
            <strong>The XO Sports Hub Team</strong>
          </p>
        </div>
      </div>
    </div>
  `;

  return {
    subject,
    message: textMessage,
    html: htmlMessage,
  };
};

/**
 * Generate offer acceptance email template
 * @param {Object} data - Email data
 * @param {string} data.buyerName - Buyer's full name
 * @param {string} data.contentTitle - Content title
 * @param {number} data.amount - Offer amount
 * @param {string} data.checkoutUrl - Checkout URL
 * @param {string} data.sellerResponse - Optional seller response message
 * @returns {string} HTML email template
 */
const offerAcceptedTemplate = (data) => {
  const { buyerName, contentTitle, amount, checkoutUrl, sellerResponse } = data;

  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f9f9f9; padding: 20px;">
      <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <!-- Header -->
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #2c5aa0; margin: 0; font-size: 28px;">🎉 Offer Accepted!</h1>
          <p style="color: #666; font-size: 18px; margin: 10px 0 0 0;">Your offer has been accepted by the seller</p>
        </div>

        <!-- Greeting -->
        <div style="margin-bottom: 25px;">
          <p style="font-size: 16px; color: #333; margin: 0;">Dear ${buyerName},</p>
        </div>

        <!-- Main Message -->
        <div style="background-color: #e8f4fd; padding: 20px; border-radius: 8px; margin-bottom: 25px; border-left: 4px solid #2c5aa0;">
          <p style="font-size: 16px; color: #333; margin: 0 0 10px 0;">
            Excellent news! Your offer of <strong style="color: #2c5aa0;">$${amount.toFixed(
    2
  )}</strong> for
            "<strong>${contentTitle}</strong>" has been accepted by the seller.
          </p>
        </div>

        ${sellerResponse
      ? `
        <!-- Seller Response -->
        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 25px; border-left: 4px solid #6c757d;">
          <h4 style="color: #495057; margin: 0 0 10px 0; font-size: 16px;">Message from Seller:</h4>
          <p style="color: #495057; margin: 0; font-style: italic;">"${sellerResponse}"</p>
        </div>
        `
      : ""
    }

        <!-- CTA Button -->
        <div style="text-align: center; margin: 30px 0;">
          <a href="${checkoutUrl}"
             style="background-color: #2c5aa0; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; font-size: 16px; display: inline-block;">
            Pay Now - $${amount.toFixed(2)}
          </a>
        </div>

        <!-- Important Notes -->
        <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107; margin-bottom: 25px;">
          <h4 style="color: #856404; margin: 0 0 10px 0; font-size: 16px;">Next Steps:</h4>
          <ul style="color: #856404; margin: 0; padding-left: 20px;">
            <li>Click the "Pay Now" button above to complete your purchase</li>
            <li>After successful payment, you'll have immediate access to preview the content</li>
            <li>The content has been reserved for you and removed from public listings</li>
            <li>If you have any questions, please contact our support team</li>
          </ul>
        </div>

        <!-- Footer -->
        <div style="text-align: center; padding-top: 20px; border-top: 1px solid #eee;">
          <p style="color: #666; font-size: 14px; margin: 0;">
            Thank you for using XO Sports Hub!<br>
            <strong>The XO Sports Hub Team</strong>
          </p>
        </div>
      </div>
    </div>
  `;
};

/**
 * Generate order receipt email template
 * @param {Object} data - Email data
 * @param {Object} data.order - Order information
 * @param {Object} data.buyer - Buyer information
 * @param {Object} data.content - Content information
 * @param {Object} data.cardDetails - Payment card details
 * @returns {Object} Email template with subject, text, and html
 */
const orderReceiptTemplate = (data) => {
  const { order, buyer, content, cardDetails } = data;

  const subject = `🧾 Order Receipt - ${content.title}`;

  const textMessage = `
Thank you for your purchase, ${buyer.firstName}!

Order Details:
- Order ID: #${order._id.toString().slice(-8)}
- Date & Time: ${formatStandardDate(order.createdAt)}

Content:
- Title: ${content.title}
- Sport: ${content.sport || "N/A"}
- Content Type: ${content.contentType || "Digital Content"}

Payment Details:
- Content Price: $${order.amount.toFixed(2)}
- Total Paid: $${order.amount.toFixed(2)}
- Payment Method: ${cardDetails?.cardType
      ? `${cardDetails.cardType.toUpperCase()} ending in ${cardDetails.lastFourDigits
      }`
      : "Card Payment"
    }
- Payment Status: ${order.paymentStatus}

Note: Platform fee is deducted from seller earnings, not added to your payment.

Customer Information:
- Name: ${buyer.firstName} ${buyer.lastName}
- Email: ${buyer.email}
${buyer.mobile ? `- Phone: ${buyer.mobile}` : ""}

You can download your content from your dashboard at any time.

If you have any questions about your order, please contact our support team.

Thank you for choosing XO Sports Hub!

Best regards,
The XO Sports Hub Team
  `;

  const htmlMessage = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f9f9f9; padding: 20px;">
      <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <!-- Header -->
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #2c5aa0; margin: 0; font-size: 28px;">🧾 Order Receipt</h1>
          <p style="color: #666; font-size: 18px; margin: 10px 0 0 0;">Thank you for your purchase!</p>
        </div>

        <!-- Greeting -->
        <div style="margin-bottom: 25px;">
          <p style="font-size: 16px; color: #333; margin: 0;">Dear ${buyer.firstName
    },</p>
        </div>

        <!-- Order Details -->
        <div style="margin-bottom: 25px;">
          <h3 style="color: #2c5aa0; margin: 0 0 15px 0; font-size: 18px;">Order Details</h3>
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="padding: 8px 0; color: #666; font-weight: bold; width: 30%;">Order ID:</td>
              <td style="padding: 8px 0; color: #333;">#${order._id
      .toString()
      .slice(-8)}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #666; font-weight: bold;">Date:</td>
              <td style="padding: 8px 0; color: #333;">${new Date(
        order.createdAt
      ).toLocaleDateString()}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #666; font-weight: bold;">Time:</td>
              <td style="padding: 8px 0; color: #333;">${new Date(
        order.createdAt
      ).toLocaleTimeString()}</td>
            </tr>
          </table>
        </div>

        <!-- Content Details -->
        <div style="margin-bottom: 25px;">
          <h3 style="color: #2c5aa0; margin: 0 0 15px 0; font-size: 18px;">Content</h3>
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="padding: 8px 0; color: #666; font-weight: bold; width: 30%;">Title:</td>
              <td style="padding: 8px 0; color: #333;">${content.title}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #666; font-weight: bold;">Sport:</td>
              <td style="padding: 8px 0; color: #333;">${content.sport || "N/A"
    }</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #666; font-weight: bold;">Content Type:</td>
              <td style="padding: 8px 0; color: #333;">${content.contentType || "Digital Content"
    }</td>
            </tr>
          </table>
        </div>

        <!-- Payment Details -->
        <div style="margin-bottom: 25px;">
          <h3 style="color: #2c5aa0; margin: 0 0 15px 0; font-size: 18px;">Payment Details</h3>
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="padding: 8px 0; color: #666; font-weight: bold; width: 30%;">Content Price:</td>
              <td style="padding: 8px 0; color: #333;">$${order.amount.toFixed(
      2
    )}</td>
            </tr>
       
            <tr>
              <td style="padding: 8px 0; color: #666; font-weight: bold;">Total Paid:</td>
              <td style="padding: 8px 0; color: #2c5aa0; font-weight: bold; font-size: 18px;">$${order.amount.toFixed(
      2
    )}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #666; font-weight: bold;">Payment Method:</td>
              <td style="padding: 8px 0; color: #333;">${cardDetails?.cardType
      ? `${cardDetails.cardType.toUpperCase()} ending in ${cardDetails.lastFourDigits
      }`
      : "Card Payment"
    }</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #666; font-weight: bold;">Payment Status:</td>
              <td style="padding: 8px 0; color: #333;">${order.paymentStatus
    }</td>
            </tr>
          </table>
          <div style="margin-top: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 5px;">
            <small style="color: #666;">
              Platform fee is deducted from seller earnings, not added to your payment.
            </small>
          </div>
        </div>

        <!-- Customer Information -->
        <div style="margin-bottom: 25px;">
          <h3 style="color: #2c5aa0; margin: 0 0 15px 0; font-size: 18px;">Customer Information</h3>
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="padding: 8px 0; color: #666; font-weight: bold; width: 30%;">Name:</td>
              <td style="padding: 8px 0; color: #333;">${buyer.firstName} ${buyer.lastName
    }</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #666; font-weight: bold;">Email:</td>
              <td style="padding: 8px 0; color: #333;">${buyer.email}</td>
            </tr>
            ${buyer.mobile
      ? `
            <tr>
              <td style="padding: 8px 0; color: #666; font-weight: bold;">Phone:</td>
              <td style="padding: 8px 0; color: #333;">${buyer.mobile}</td>
            </tr>
            `
      : ""
    }
          </table>
        </div>

        <!-- Important Notes -->
        <div style="background-color: #e8f4fd; padding: 15px; border-radius: 5px; margin-bottom: 25px; border-left: 4px solid #2c5aa0;">
          <p style="color: #333; margin: 0;">
            You can download your content from your dashboard at any time. If you have any questions about your order, please contact our support team.
          </p>
        </div>

        <!-- Footer -->
        <div style="text-align: center; padding-top: 20px; border-top: 1px solid #eee;">
          <p style="color: #666; font-size: 14px; margin: 0;">
            Thank you for choosing XO Sports Hub!<br>
            <strong>The XO Sports Hub Team</strong>
          </p>
        </div>
      </div>
    </div>
  `;

  return {
    subject,
    message: textMessage,
    html: htmlMessage,
  };
};

/**
 * Generate bid rejection email template
 * @param {Object} data - Email data
 * @param {Object} data.bidder - Bidder information
 * @param {Object} data.content - Content information
 * @param {Object} data.bid - Bid information
 * @param {string} data.sellerResponse - Seller's rejection message
 * @returns {Object} Email template with subject, text, and html
 */
const generateBidRejectionEmail = (data) => {
  const { bidder, content, bid, sellerResponse } = data;

  const subject = `Your bid was not accepted - ${content.title}`;

  const textMessage = `
Dear ${bidder.firstName},

We regret to inform you that your bid for "${content.title}" was not accepted by the seller.

Bid Details:
- Title: ${content.title}
- Sport: ${content.sport}
- Content Type: ${content.contentType}
- Your Bid Amount: $${bid.amount.toFixed(2)}
- Seller: ${content.seller.firstName} ${content.seller.lastName}

${sellerResponse ? `Message from Seller:\n"${sellerResponse}"\n` : ''}

Don't be discouraged! There are many other great sports content and strategies available on XO Sports Hub. 

You can:
- Browse other content in the ${content.sport} category
- Make offers on fixed-price content
- Participate in other auctions

Thank you for participating in our auction system.

Best regards,
The XO Sports Hub Team
  `;

  const htmlMessage = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f9f9f9; padding: 20px;">
      <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <!-- Header -->
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #dc2626; margin: 0; font-size: 28px;">Bid Not Accepted</h1>
          <p style="color: #666; font-size: 18px; margin: 10px 0 0 0;">Thank you for your participation</p>
        </div>

        <!-- Greeting -->
        <div style="margin-bottom: 25px;">
          <p style="font-size: 16px; color: #333; margin: 0;">Dear ${bidder.firstName},</p>
        </div>

        <!-- Main Message -->
        <div style="background-color: #fef2f2; padding: 20px; border-radius: 8px; margin-bottom: 25px; border-left: 4px solid #dc2626;">
          <p style="font-size: 16px; color: #333; margin: 0 0 10px 0;">
            We regret to inform you that your bid of <strong style="color: #dc2626;">$${bid.amount.toFixed(2)}</strong> for 
            "<strong>${content.title}</strong>" was not accepted by the seller.
          </p>
        </div>

        <!-- Bid Details -->
        <div style="margin-bottom: 25px;">
          <h3 style="color: #dc2626; margin: 0 0 15px 0; font-size: 18px;">Bid Details</h3>
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="padding: 8px 0; color: #666; font-weight: bold; width: 30%;">Title:</td>
              <td style="padding: 8px 0; color: #333;">${content.title}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #666; font-weight: bold;">Sport:</td>
              <td style="padding: 8px 0; color: #333;">${content.sport}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #666; font-weight: bold;">Content Type:</td>
              <td style="padding: 8px 0; color: #333;">${content.contentType}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #666; font-weight: bold;">Your Bid:</td>
              <td style="padding: 8px 0; color: #dc2626; font-weight: bold; font-size: 18px;">$${bid.amount.toFixed(2)}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #666; font-weight: bold;">Seller:</td>
              <td style="padding: 8px 0; color: #333;">${content.seller.firstName} ${content.seller.lastName}</td>
            </tr>
          </table>
        </div>

        ${sellerResponse ? `
        <!-- Seller Response -->
        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 25px; border-left: 4px solid #6c757d;">
          <h4 style="color: #495057; margin: 0 0 10px 0; font-size: 16px;">Message from Seller:</h4>
          <p style="color: #495057; margin: 0; font-style: italic;">"${sellerResponse}"</p>
        </div>
        ` : ''}

        <!-- Encouragement Section -->
        <div style="background-color: #ecfdf5; padding: 20px; border-radius: 8px; margin-bottom: 25px; border-left: 4px solid #10b981;">
          <h3 style="color: #047857; margin: 0 0 15px 0; font-size: 18px;">Don't Give Up!</h3>
          <p style="color: #047857; margin: 0 0 15px 0;">
            There are many other great sports content and strategies available on XO Sports Hub.
          </p>
          <ul style="color: #047857; margin: 0; padding-left: 20px;">
            <li>Browse other content in the ${content.sport} category</li>
            <li>Make offers on fixed-price content</li>
            <li>Participate in other auctions</li>
          </ul>
        </div>

        <!-- Footer -->
        <div style="text-align: center; padding-top: 20px; border-top: 1px solid #eee;">
          <p style="color: #666; font-size: 14px; margin: 0;">
            Thank you for participating in our auction system!<br>
            <strong>The XO Sports Hub Team</strong>
          </p>
        </div>
      </div>
    </div>
  `;

  return {
    subject,
    message: textMessage,
    html: htmlMessage,
  };
};

/**
 * Generate offer rejection email template
 * @param {Object} data - Email data
 * @param {string} data.buyerName - Buyer's full name
 * @param {string} data.contentTitle - Content title
 * @param {number} data.amount - Offer amount
 * @param {string} data.sellerResponse - Seller's rejection message
 * @param {string} data.contentSport - Content sport category
 * @returns {string} HTML email template
 */
const offerRejectedTemplate = (data) => {
  const { buyerName, contentTitle, amount, sellerResponse, contentSport } = data;

  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f9f9f9; padding: 20px;">
      <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <!-- Header -->
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #dc2626; margin: 0; font-size: 28px;">Offer Not Accepted</h1>
          <p style="color: #666; font-size: 18px; margin: 10px 0 0 0;">Thank you for your interest</p>
        </div>

        <!-- Greeting -->
        <div style="margin-bottom: 25px;">
          <p style="font-size: 16px; color: #333; margin: 0;">Dear ${buyerName},</p>
        </div>

        <!-- Main Message -->
        <div style="background-color: #fef2f2; padding: 20px; border-radius: 8px; margin-bottom: 25px; border-left: 4px solid #dc2626;">
          <p style="font-size: 16px; color: #333; margin: 0 0 10px 0;">
            We regret to inform you that your offer of <strong style="color: #dc2626;">$${amount.toFixed(2)}</strong> for
            "<strong>${contentTitle}</strong>" was not accepted by the seller.
          </p>
        </div>

        ${sellerResponse ? `
        <!-- Seller Response -->
        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 25px; border-left: 4px solid #6c757d;">
          <h4 style="color: #495057; margin: 0 0 10px 0; font-size: 16px;">Message from Seller:</h4>
          <p style="color: #495057; margin: 0; font-style: italic;">"${sellerResponse}"</p>
        </div>
        ` : ''}

        <!-- Encouragement Section -->
        <div style="background-color: #ecfdf5; padding: 20px; border-radius: 8px; margin-bottom: 25px; border-left: 4px solid #10b981;">
          <h3 style="color: #047857; margin: 0 0 15px 0; font-size: 18px;">Keep Exploring!</h3>
          <p style="color: #047857; margin: 0 0 15px 0;">
            There are many other excellent sports content and strategies available on XO Sports Hub.
          </p>
          <ul style="color: #047857; margin: 0; padding-left: 20px;">
            <li>Browse more content in the ${contentSport || 'sports'} category</li>
            <li>Make new offers on other content</li>
            <li>Participate in live auctions</li>
            <li>Follow your favorite coaches and sellers</li>
          </ul>
        </div>

        <!-- Footer -->
        <div style="text-align: center; padding-top: 20px; border-top: 1px solid #eee;">
          <p style="color: #666; font-size: 14px; margin: 0;">
            Thank you for using XO Sports Hub!<br>
            <strong>The XO Sports Hub Team</strong>
          </p>
        </div>
      </div>
    </div>
  `;
};

/**
 * Generate password reset email template
 * @param {Object} data - Email data
 * @param {Object} data.user - User information
 * @param {string} data.resetUrl - Password reset URL
 * @returns {Object} Email template with subject, text, and html
 */
const passwordResetTemplate = (data) => {
  const { user, resetUrl } = data;

  const subject = '🔐 Password Reset Request - XO Sports Hub';

  const textMessage = `
Hi ${user.firstName},

You have requested to reset your password for your XO Sports Hub account.

Please click the following link to reset your password:
${resetUrl}

This link will expire in 10 minutes for security reasons.

If you did not request this password reset, please ignore this email and your password will remain unchanged.

Best regards,
The XO Sports Hub Team
  `;

  const htmlMessage = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f9f9f9; padding: 20px;">
      <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <!-- Header -->
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #ec1d3b; margin: 0; font-size: 28px;">🔐 Password Reset</h1>
          <p style="color: #666; font-size: 18px; margin: 10px 0 0 0;">XO Sports Hub</p>
        </div>

        <!-- Greeting -->
        <div style="margin-bottom: 25px;">
          <p style="font-size: 16px; color: #333; margin: 0;">Hi ${user.firstName},</p>
        </div>

        <!-- Content -->
        <div style="margin-bottom: 30px;">
          <p style="font-size: 16px; color: #333; line-height: 1.6; margin: 0 0 15px 0;">
            You have requested to reset your password for your XO Sports Hub account.
          </p>
          <p style="font-size: 16px; color: #333; line-height: 1.6; margin: 0 0 20px 0;">
            Please click the button below to reset your password:
          </p>

          <!-- Reset Button -->
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}" style="background-color: #ec1d3b; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
              Reset Password
            </a>
          </div>

          <p style="font-size: 14px; color: #666; line-height: 1.6; margin: 20px 0 0 0;">
            <strong>Important:</strong> This link will expire in 10 minutes for security reasons.
          </p>
          <p style="font-size: 14px; color: #666; line-height: 1.6; margin: 10px 0 0 0;">
            If you did not request this password reset, please ignore this email and your password will remain unchanged.
          </p>
        </div>

        <!-- Footer -->
        <div style="text-align: center; padding-top: 20px; border-top: 1px solid #eee;">
          <p style="color: #666; font-size: 14px; margin: 0;">
            Best regards,<br>
            <strong>The XO Sports Hub Team</strong>
          </p>
        </div>
      </div>
    </div>
  `;

  return {
    subject,
    message: textMessage,
    html: htmlMessage
  };
};

/**
 * Generate password change confirmation email template
 * @param {Object} data - Email data
 * @param {Object} data.user - User information
 * @returns {Object} Email template with subject, text, and html
 */
const passwordChangeConfirmationTemplate = (data) => {
  const { user } = data;

  const subject = '✅ Password Changed Successfully - XO Sports Hub';

  const textMessage = `
Hi ${user.firstName},

Your password has been successfully changed for your XO Sports Hub account.

If you did not make this change, please contact our support team immediately.

Best regards,
The XO Sports Hub Team
  `;

  const htmlMessage = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f9f9f9; padding: 20px;">
      <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <!-- Header -->
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #28a745; margin: 0; font-size: 28px;">✅ Password Changed</h1>
          <p style="color: #666; font-size: 18px; margin: 10px 0 0 0;">XO Sports Hub</p>
        </div>

        <!-- Greeting -->
        <div style="margin-bottom: 25px;">
          <p style="font-size: 16px; color: #333; margin: 0;">Hi ${user.firstName},</p>
        </div>

        <!-- Content -->
        <div style="margin-bottom: 30px;">
          <p style="font-size: 16px; color: #333; line-height: 1.6; margin: 0 0 15px 0;">
            Your password has been successfully changed for your XO Sports Hub account.
          </p>
          <p style="font-size: 14px; color: #666; line-height: 1.6; margin: 15px 0 0 0;">
            If you did not make this change, please contact our support team immediately.
          </p>
        </div>

        <!-- Footer -->
        <div style="text-align: center; padding-top: 20px; border-top: 1px solid #eee;">
          <p style="color: #666; font-size: 14px; margin: 0;">
            Best regards,<br>
            <strong>The XO Sports Hub Team</strong>
          </p>
        </div>
      </div>
    </div>
  `;

  return {
    subject,
    message: textMessage,
    html: htmlMessage
  };
};

module.exports = {
  generateBidAcceptanceEmail,
  generateBidRejectionEmail,
  offerAcceptedTemplate,
  offerRejectedTemplate,
  orderReceiptTemplate,
  passwordResetTemplate,
  passwordChangeConfirmationTemplate,
};
