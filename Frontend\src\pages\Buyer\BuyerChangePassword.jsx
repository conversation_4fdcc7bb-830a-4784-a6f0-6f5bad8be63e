import React from "react";
import SectionWrapper from "../../components/common/SectionWrapper";
import ChangePassword from "../../components/common/ChangePassword";
import { FaLock } from "react-icons/fa";

const BuyerChangePassword = () => {
  return (
    <div className="BuyerChangePassword">
      <SectionWrapper
        icon={<FaLock className="BuyerSidebar__icon" />}
        title="Change Password"
      >
        <div className="profile_border_container">
          <ChangePassword />
        </div>
      </SectionWrapper>
    </div>
  );
};

export default BuyerChangePassword;
