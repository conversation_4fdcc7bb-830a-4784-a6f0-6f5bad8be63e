#!/usr/bin/env node

/**
 * Migration Runner for XOSportsHub Password Authentication
 * 
 * This script safely migrates your application from OTP-based to password-based authentication.
 * It includes safety checks and backup recommendations.
 */

const readline = require('readline');
const { runMigration } = require('./migrateToPasswordAuth');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const askQuestion = (question) => {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.toLowerCase().trim());
    });
  });
};

const displayWarning = () => {
  console.log('\n🚨 IMPORTANT: PASSWORD AUTHENTICATION MIGRATION');
  console.log('===============================================');
  console.log('');
  console.log('This migration will:');
  console.log('✅ Add password fields to all existing users');
  console.log('✅ Generate temporary passwords for existing users');
  console.log('✅ Remove OTP-related fields from the database');
  console.log('✅ Clean up temporary user records');
  console.log('');
  console.log('⚠️  BEFORE PROCEEDING:');
  console.log('1. 📦 Create a backup of your MongoDB database');
  console.log('2. 🛑 Stop your application server');
  console.log('3. 📧 Prepare to send temporary passwords to users');
  console.log('4. 🔄 Update your frontend code to use email/password login');
  console.log('');
  console.log('💡 After migration:');
  console.log('- Users will need to login with email + temporary password');
  console.log('- Instruct users to change their passwords after first login');
  console.log('- OTP/SMS authentication will no longer work');
  console.log('');
};

const confirmBackup = async () => {
  console.log('🔍 Database Backup Verification');
  console.log('==============================');
  console.log('');
  console.log('Have you created a backup of your MongoDB database?');
  console.log('');
  console.log('To create a backup, run:');
  console.log('mongodump --uri="your_mongodb_connection_string" --out=./backup');
  console.log('');
  
  const hasBackup = await askQuestion('Do you have a recent database backup? (yes/no): ');
  
  if (hasBackup !== 'yes' && hasBackup !== 'y') {
    console.log('');
    console.log('❌ Please create a database backup before proceeding.');
    console.log('   This migration modifies user data and cannot be easily reversed.');
    console.log('');
    process.exit(1);
  }
  
  console.log('✅ Backup confirmed');
};

const confirmEnvironment = async () => {
  console.log('');
  console.log('🌍 Environment Verification');
  console.log('===========================');
  console.log('');
  console.log('Current environment variables:');
  console.log(`- NODE_ENV: ${process.env.NODE_ENV || 'not set'}`);
  console.log(`- MONGODB_URI: ${process.env.MONGODB_URI ? 'configured' : 'NOT SET'}`);
  console.log('');
  
  if (!process.env.MONGODB_URI) {
    console.log('❌ MONGODB_URI environment variable is not set.');
    console.log('   Please set it before running the migration.');
    process.exit(1);
  }
  
  const isProduction = process.env.NODE_ENV === 'production';
  if (isProduction) {
    console.log('⚠️  You are running this migration in PRODUCTION environment!');
    console.log('');
    const confirmProd = await askQuestion('Are you sure you want to proceed in production? (yes/no): ');
    
    if (confirmProd !== 'yes' && confirmProd !== 'y') {
      console.log('❌ Migration cancelled for safety.');
      process.exit(1);
    }
  }
  
  console.log('✅ Environment verified');
};

const finalConfirmation = async () => {
  console.log('');
  console.log('🚀 Final Confirmation');
  console.log('=====================');
  console.log('');
  console.log('You are about to migrate your authentication system.');
  console.log('This will affect ALL users in your database.');
  console.log('');
  console.log('After this migration:');
  console.log('- Users will login with email + password (not mobile + OTP)');
  console.log('- You will receive a report with temporary passwords');
  console.log('- You must update your frontend application');
  console.log('');
  
  const finalConfirm = await askQuestion('Type "MIGRATE" to proceed with the migration: ');
  
  if (finalConfirm !== 'migrate') {
    console.log('❌ Migration cancelled.');
    process.exit(1);
  }
  
  console.log('✅ Migration confirmed');
};

const main = async () => {
  try {
    displayWarning();
    
    await confirmBackup();
    await confirmEnvironment();
    await finalConfirmation();
    
    console.log('');
    console.log('🔄 Starting migration...');
    console.log('');
    
    // Run the actual migration
    await runMigration();
    
    console.log('');
    console.log('🎉 Migration completed successfully!');
    console.log('');
    console.log('📋 Next Steps:');
    console.log('1. 📧 Send temporary passwords to your users');
    console.log('2. 🔄 Update and deploy your frontend application');
    console.log('3. 🚀 Start your application server');
    console.log('4. 📢 Notify users about the authentication change');
    console.log('');
    
  } catch (error) {
    console.error('💥 Migration failed:', error.message);
    console.log('');
    console.log('🔧 Troubleshooting:');
    console.log('1. Check your database connection');
    console.log('2. Ensure you have proper permissions');
    console.log('3. Restore from backup if needed');
    console.log('4. Contact support if issues persist');
    process.exit(1);
  } finally {
    rl.close();
  }
};

// Run the migration if this file is executed directly
if (require.main === module) {
  main();
}

module.exports = { main };
