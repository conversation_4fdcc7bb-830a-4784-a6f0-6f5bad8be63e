const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const crypto = require('crypto');
require('dotenv').config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ MongoDB connected successfully');
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error);
    process.exit(1);
  }
};

// User schema for migration
const UserSchema = new mongoose.Schema({
  firstName: String,
  lastName: String,
  email: String,
  mobile: String,
  password: String,
  role: String,
  activeRole: String,
  isVerified: Boolean,
  status: Number,
  // Old OTP fields to remove
  otpCode: String,
  otpExpire: Date,
  // New password reset fields
  resetPasswordToken: String,
  resetPasswordExpire: Date,
  // Other fields...
}, { strict: false });

const User = mongoose.model('User', UserSchema);

// TempUser schema for cleanup
const TempUserSchema = new mongoose.Schema({}, { strict: false });
const TempUser = mongoose.model('TempUser', TempUserSchema);

/**
 * Generate a temporary password for existing users
 * @param {Object} user - User object
 * @returns {string} Generated password
 */
const generateTempPassword = (user) => {
  // Special case for admin users - give them a more secure default password
  if (user.role === 'admin') {
    return 'Admin123!';
  }

  // Generate a password based on user's name and a random string
  const randomString = crypto.randomBytes(4).toString('hex');
  return `${user.firstName}${randomString}`;
};

/**
 * Hash password using bcrypt
 * @param {string} password - Plain text password
 * @returns {string} Hashed password
 */
const hashPassword = async (password) => {
  const salt = await bcrypt.genSalt(10);
  return await bcrypt.hash(password, salt);
};

/**
 * Migrate users from OTP-based to password-based authentication
 */
const migrateUsers = async () => {
  try {
    console.log('🔄 Starting user migration...');

    // Find all users without passwords
    const usersWithoutPassword = await User.find({
      $or: [
        { password: { $exists: false } },
        { password: null },
        { password: '' }
      ]
    });

    console.log(`📊 Found ${usersWithoutPassword.length} users to migrate`);

    const migrationResults = [];

    for (const user of usersWithoutPassword) {
      try {
        // Generate temporary password
        const tempPassword = generateTempPassword(user);
        const hashedPassword = await hashPassword(tempPassword);

        // Update user with password and remove OTP fields
        await User.findByIdAndUpdate(user._id, {
          $set: {
            password: hashedPassword,
            isVerified: true, // Auto-verify existing users
          },
          $unset: {
            otpCode: 1,
            otpExpire: 1
          }
        }, {
          runValidators: false // Skip validation to avoid issues with existing data
        });

        migrationResults.push({
          userId: user._id,
          email: user.email,
          tempPassword: tempPassword,
          status: 'success'
        });

        console.log(`✅ Migrated user: ${user.email}`);
      } catch (error) {
        console.error(`❌ Failed to migrate user ${user.email}:`, error);
        migrationResults.push({
          userId: user._id,
          email: user.email,
          status: 'failed',
          error: error.message
        });
      }
    }

    return migrationResults;
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
};

/**
 * Clean up temporary users collection
 */
const cleanupTempUsers = async () => {
  try {
    console.log('🧹 Cleaning up temporary users...');
    const result = await TempUser.deleteMany({});
    console.log(`✅ Deleted ${result.deletedCount} temporary users`);
  } catch (error) {
    console.error('❌ Failed to cleanup temporary users:', error);
  }
};

/**
 * Generate migration report
 * @param {Array} results - Migration results
 */
const generateReport = (results) => {
  const successful = results.filter(r => r.status === 'success');
  const failed = results.filter(r => r.status === 'failed');

  console.log('\n📋 MIGRATION REPORT');
  console.log('==================');
  console.log(`Total users processed: ${results.length}`);
  console.log(`Successfully migrated: ${successful.length}`);
  console.log(`Failed migrations: ${failed.length}`);

  if (successful.length > 0) {
    console.log('\n✅ SUCCESSFULLY MIGRATED USERS:');
    console.log('Email\t\t\tTemporary Password');
    console.log('-----\t\t\t------------------');
    successful.forEach(user => {
      console.log(`${user.email}\t\t${user.tempPassword}`);
    });

    console.log('\n⚠️  IMPORTANT NOTES:');
    console.log('1. Users have been assigned temporary passwords shown above');
    console.log('2. Send these passwords to users securely (email/SMS)');
    console.log('3. Instruct users to change their passwords after first login');
    console.log('4. Consider implementing a "force password change" feature');
  }

  if (failed.length > 0) {
    console.log('\n❌ FAILED MIGRATIONS:');
    failed.forEach(user => {
      console.log(`${user.email}: ${user.error}`);
    });
  }
};

/**
 * Main migration function
 */
const runMigration = async () => {
  try {
    await connectDB();

    console.log('🚀 Starting migration to password-based authentication...\n');

    // Migrate users
    const results = await migrateUsers();

    // Cleanup temporary users
    await cleanupTempUsers();

    // Generate report
    generateReport(results);

    console.log('\n🎉 Migration completed successfully!');

  } catch (error) {
    console.error('💥 Migration failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('📡 Database connection closed');
  }
};

// Run migration if this file is executed directly
if (require.main === module) {
  runMigration();
}

module.exports = {
  runMigration,
  migrateUsers,
  cleanupTempUsers,
  generateTempPassword,
  hashPassword
};
