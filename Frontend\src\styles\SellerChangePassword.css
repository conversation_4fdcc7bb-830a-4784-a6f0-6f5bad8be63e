.seller-change-password-container {
  width: 100%;
  padding: var(--basefont);
}

.seller-change-password-content {
  max-width: 600px;
  margin: 0 auto;
}

.seller-change-password-header {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  margin-bottom: var(--heading5);
  padding-bottom: var(--basefont);
  border-bottom: 1px solid var(--light-gray);
}

.seller-change-password-icon {
  color: var(--btn-color);
  font-size: var(--heading4);
}

.seller-change-password-header h1 {
  color: var(--text-color);
  font-size: var(--heading3);
  font-weight: 600;
  margin: 0;
}

.seller-change-password-form {
  background: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  padding: var(--heading5);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Override ChangePassword styles for seller layout */
.seller-change-password-form .ChangePassword__container {
  justify-content: flex-start;
}

.seller-change-password-form .ChangePassword__form-section {
  max-width: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
  .seller-change-password-container {
    padding: var(--smallfont);
  }
  
  .seller-change-password-content {
    max-width: 100%;
  }
  
  .seller-change-password-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--smallfont);
  }
  
  .seller-change-password-header h1 {
    font-size: var(--heading4);
  }
  
  .seller-change-password-form {
    padding: var(--basefont);
  }
}

@media (max-width: 480px) {
  .seller-change-password-header h1 {
    font-size: var(--heading5);
  }
  
  .seller-change-password-icon {
    font-size: var(--heading5);
  }
}
