import React, { useState } from "react";
import { useNavigate, <PERSON> } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import "../../styles/Auth.css";
import { login, reset, googleSignIn } from "../../redux/slices/authSlice";
import { APP_CONFIG } from "../../utils/constants";
import toast from "../../utils/toast";
import GoogleSignInButton from "../../components/common/GoogleSignInButton";
import { FaEnvelope, FaLock, FaEye, FaEyeSlash } from "react-icons/fa";
import firebaseService from "../../services/firebaseService";
import { getSellerRedirectPath } from "../../utils/sellerUtils";

const Auth = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { isLoading } = useSelector(
    (state) => state.auth
  );

  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });

  const [errors, setErrors] = useState({});
  const [showPassword, setShowPassword] = useState(false);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value,
    });

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null,
      });
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const validateForm = () => {
    const newErrors = {};

    // Validate email
    if (!formData.email) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    // Validate password
    if (!formData.password) {
      newErrors.password = "Password is required";
    }

    return newErrors;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    // Reset any previous errors
    dispatch(reset());

    try {
      // Dispatch login action
      const result = await dispatch(login({
        email: formData.email,
        password: formData.password
      })).unwrap();

      // Show success message
      toast.auth.loginSuccess();

      // Navigate based on user role and onboarding status
      const userRole = result.user?.role || 'buyer';
      if (userRole === 'seller') {
        // For sellers, check onboarding status and redirect accordingly
        const redirectPath = getSellerRedirectPath(result.user);
        navigate(redirectPath);
      } else if (userRole === 'admin') {
        navigate("/admin/dashboard");
      } else {
        navigate("/content");
      }
    } catch (error) {
      console.error("Login error:", error);

      // Extract the error message from the formatted error object
      let errorMessage = "Login failed. Please try again.";

      if (typeof error === 'string') {
        errorMessage = error;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      // Handle different types of errors
      if (errorMessage.includes("Invalid credentials")) {
        toast.error("Invalid email or password. Please try again.");
      } else if (errorMessage.includes("Account is not active")) {
        toast.error("Your account is not active. Please contact support.");
      } else {
        // Show the actual backend error message
        toast.error(errorMessage);
      }
    }
  };

  // Handle Google Sign-In
  const handleGoogleSignIn = async () => {
    try {
      dispatch(reset());

      // Check if Firebase is initialized
      if (!firebaseService.isInitialized()) {
        toast.error(
          "Firebase is not initialized. Please check your configuration."
        );
        return;
      }

      // Sign in with Google using Firebase
      const result = await firebaseService.signInWithGoogle();

      // Try to sign in with existing account
      try {
        const response = await dispatch(googleSignIn(result.idToken)).unwrap();

        // Success - user exists and is logged in
        toast.auth.loginSuccess();

        // Navigate based on user role and onboarding status
        if (response.user.role === "buyer") {
          navigate("/content");
        } else if (response.user.role === "seller") {
          // For sellers, check onboarding status and redirect accordingly
          const redirectPath = getSellerRedirectPath(response.user);
          navigate(redirectPath);
        } else if (response.user.role === "admin") {
          navigate("/admin/dashboard");
        } else {
          navigate("/");
        }
      } catch (signInError) {
        // User doesn't exist - automatically create account with default "buyer" role
        const errorMessage =
          typeof signInError === "string"
            ? signInError
            : signInError?.message || "";
        if (
          errorMessage.includes("not found") ||
          errorMessage.includes("does not exist")
        ) {
          // Automatically create user as buyer (default role)
          try {
            const { googleSignUp } = await import("../../redux/slices/authSlice");

            await dispatch(
              googleSignUp({ idToken: result.idToken, role: "buyer" })
            ).unwrap();

            // Success - user created and logged in as buyer
            toast.auth.registrationSuccess();

            // Navigate to buyer dashboard
            navigate("/content");
          } catch (signUpError) {
            console.error("Google sign-up error:", signUpError);

            // Extract the error message from the formatted error object
            let errorMessage = "Failed to complete registration. Please try again.";

            if (typeof signUpError === "string") {
              errorMessage = signUpError;
            } else if (signUpError?.message) {
              errorMessage = signUpError.message;
            }

            // Show the actual backend error message
            toast.error(errorMessage);
          }
        } else {
          throw signInError;
        }
      }
    } catch (error) {
      console.error("Google sign-in error:", error);

      // Extract the error message from the formatted error object
      let errorMessage = "Failed to sign in with Google. Please try again.";

      if (typeof error === "string") {
        errorMessage = error;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      // Show the actual backend error message
      toast.error(errorMessage);
    }
  };



  return (
    <div className="auth-page auth-container">
      <div className="auth-form-container">
        <h1 className="auth-title">Login in to your account</h1>

        <form onSubmit={handleSubmit} className="auth-form">
          <div className="auth-form-input">
            <div className="email-input-container">
              <FaEnvelope className="email-icon" />
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="Enter your email"
                className={`form-input email-input ${errors.email ? 'input-error' : ''}`}
                required
              />
            </div>
            {errors.email && <p className="error-message">{errors.email}</p>}
          </div>

          <div className="auth-form-input">
            <div className="email-input-container">
              <FaLock className="email-icon" />
              <input
                type={showPassword ? "text" : "password"}
                name="password"
                value={formData.password}
                onChange={handleChange}
                placeholder="Enter your password"
                className={`form-input email-input ${errors.password ? 'input-error' : ''}`}
                required
              />
              <button
                type="button"
                className="password-toggle"
                onClick={togglePasswordVisibility}
              >
                {showPassword ? <FaEyeSlash /> : <FaEye />}
              </button>
            </div>
            {errors.password && <p className="error-message">{errors.password}</p>}
          </div>

          <div className="auth-options">
            <div></div>
            <Link to="/forgot-password" className="forgot-password">Forgot Password?</Link>
          </div>

          <button type="submit" className="signin-button" disabled={isLoading}>
            {isLoading ? "Signing In..." : "Sign In"}
          </button>

          <div className="auth-divider">
            <span>or</span>
          </div>

          <GoogleSignInButton
            onClick={handleGoogleSignIn}
            isLoading={isLoading}
            text="Sign in with Google"
            variant="secondary"
          />

          <p className="signup-link mt-10">
            Don't have an account? <Link to="/signup">Sign Up</Link>
          </p>
        </form>
      </div>
    </div>
  );
};

export default Auth;
