import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import "../../styles/Auth.css";
import { forgotPassword, reset } from "../../redux/slices/authSlice";
import { APP_CONFIG } from "../../utils/constants";
import toast from "../../utils/toast";
import { FaEnvelope, FaArrowLeft } from "react-icons/fa";

const ForgotPassword = () => {
  const dispatch = useDispatch();
  const { isLoading } = useSelector((state) => state.auth);

  const [email, setEmail] = useState("");
  const [errors, setErrors] = useState({});
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleChange = (e) => {
    setEmail(e.target.value);

    // Clear error when user starts typing
    if (errors.email) {
      setErrors({});
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Validate email
    if (!email) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = "Please enter a valid email address";
    }

    return newErrors;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    // Reset any previous errors
    dispatch(reset());

    try {
      await dispatch(forgotPassword({ email })).unwrap();

      // Show success message
      toast.success("Password reset email sent! Please check your inbox.");
      setIsSubmitted(true);
    } catch (error) {
      console.error("Forgot password error:", error);

      // Extract the error message from the formatted error object
      let errorMessage = "Failed to send reset email. Please try again.";

      if (typeof error === 'string') {
        errorMessage = error;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      // Handle different types of errors
      if (errorMessage.includes("no user with that email")) {
        toast.error("No account found with this email address.");
      } else {
        // Show the actual backend error message
        toast.error(errorMessage);
      }
    }
  };

  if (isSubmitted) {
    return (
      <div className="auth-page auth-container">
        <div className="auth-form-container">
          <h1 className="auth-title">Check Your Email</h1>
          <div style={{ textAlign: 'center', padding: '20px 0' }}>
            <p style={{ color: 'var(--text-color)', fontSize: 'var(--basefont)', marginBottom: '1rem', lineHeight: '1.6' }}>
              We've sent a password reset link to <strong>{email}</strong>
            </p>
            <p style={{ color: 'var(--dark-gray)', fontSize: 'var(--smallfont)', marginBottom: '2rem', lineHeight: '1.6' }}>
              Please check your inbox and click the link to reset your password.
              The link will expire in 10 minutes for security reasons.
            </p>

            <Link to="/auth" className="signin-button" style={{ marginBottom: '1rem', display: 'inline-flex', alignItems: 'center', gap: '0.5rem' }}>
              <FaArrowLeft /> Back to Login
            </Link>

            <p style={{ color: 'var(--dark-gray)', fontSize: 'var(--extrasmallfont)', marginTop: '1.5rem' }}>
              Didn't receive the email? Check your spam folder or{" "}
              <button
                type="button"
                style={{ background: 'none', border: 'none', color: 'var(--primary-color)', textDecoration: 'underline', cursor: 'pointer', fontSize: 'inherit', padding: 0 }}
                onClick={() => setIsSubmitted(false)}
              >
                try again
              </button>
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="auth-page auth-container">
      <div className="auth-form-container">
        <div style={{ marginBottom: '1rem' }}>
          <Link to="/auth" style={{ display: 'inline-flex', alignItems: 'center', gap: '0.5rem', color: 'var(--dark-gray)', textDecoration: 'none', fontSize: 'var(--smallfont)', transition: 'color 0.2s ease' }}>
            <FaArrowLeft /> Back to Login
          </Link>
        </div>

        <h1 className="auth-title">Forgot Password</h1>
        <p style={{ textAlign: 'center', color: 'var(--dark-gray)', fontSize: 'var(--smallfont)', marginBottom: '1rem', lineHeight: '1.5' }}>
          Enter your email address and we'll send you a link to reset your password.
        </p>

        <form onSubmit={handleSubmit} className="auth-form">
          <div className="auth-form-input">
            <div className="email-input-container">
              <FaEnvelope className="email-icon" />
              <input
                type="email"
                name="email"
                value={email}
                onChange={handleChange}
                placeholder="Enter your email"
                className={`form-input email-input ${errors.email ? 'input-error' : ''}`}
                required
              />
            </div>
            {errors.email && <p className="error-message">{errors.email}</p>}
          </div>

          <button type="submit" className="signin-button" disabled={isLoading}>
            {isLoading ? "Sending..." : "Send Reset Link"}
          </button>

          <p className="signup-link">
            Remember your password? <Link to="/auth">Sign In</Link>
          </p>
        </form>
      </div>
    </div>
  );
};

export default ForgotPassword;
