import React, { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate, Link } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import "../../styles/Auth.css";
import { resetPassword, reset } from "../../redux/slices/authSlice";
import { APP_CONFIG } from "../../utils/constants";
import toast from "../../utils/toast";
import { FaLock, FaEye, FaEyeSlash, FaArrowLeft } from "react-icons/fa";
import { getSellerRedirectPath } from "../../utils/sellerUtils";

const ResetPassword = () => {
  const { token } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { isLoading } = useSelector((state) => state.auth);

  const [formData, setFormData] = useState({
    password: "",
    confirmPassword: "",
  });
  const [errors, setErrors] = useState({});
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  useEffect(() => {
    // Check if token exists
    if (!token) {
      toast.error("Invalid reset link");
      navigate("/login");
    }
  }, [token, navigate]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null,
      });
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  const validateForm = () => {
    const newErrors = {};

    // Validate password
    if (!formData.password) {
      newErrors.password = "Password is required";
    } else if (formData.password.length < 6) {
      newErrors.password = "Password must be at least 6 characters";
    }

    // Validate confirm password
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = "Please confirm your password";
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
    }

    return newErrors;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    // Reset any previous errors
    dispatch(reset());

    try {
      const result = await dispatch(resetPassword({
        token,
        password: formData.password
      })).unwrap();

      // Show success message
      toast.success("Password reset successfully! You are now logged in.");

      // Navigate based on user role and onboarding status
      const userRole = result.user?.role || 'buyer';
      if (userRole === 'seller') {
        // For sellers, check onboarding status and redirect accordingly
        const redirectPath = getSellerRedirectPath(result.user);
        navigate(redirectPath);
      } else if (userRole === 'admin') {
        navigate("/admin/dashboard");
      } else {
        navigate("/content");
      }
    } catch (error) {
      console.error("Reset password error:", error);

      // Extract the error message from the formatted error object
      let errorMessage = "Failed to reset password. Please try again.";

      if (typeof error === 'string') {
        errorMessage = error;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      // Handle different types of errors
      if (errorMessage.includes("Invalid token")) {
        toast.error("Reset link is invalid or has expired. Please request a new one.");
        navigate("/forgot-password");
      } else {
        // Show the actual backend error message
        toast.error(errorMessage);
      }
    }
  };

  return (
    <div className="auth-page auth-container">
      <div className="auth-form-container">
        <div style={{ marginBottom: '1rem' }}>
          <Link to="/auth" style={{ display: 'inline-flex', alignItems: 'center', gap: '0.5rem', color: 'var(--dark-gray)', textDecoration: 'none', fontSize: 'var(--smallfont)', transition: 'color 0.2s ease' }}>
            <FaArrowLeft /> Back to Login
          </Link>
        </div>

        <h1 className="auth-title">Reset Password</h1>
        <p style={{ textAlign: 'center', color: 'var(--dark-gray)', fontSize: 'var(--smallfont)', marginBottom: '2rem', lineHeight: '1.5' }}>
          Enter your new password below.
        </p>

        <form onSubmit={handleSubmit} className="auth-form">
          <div className="auth-form-input">
            <div className="email-input-container">
              <FaLock className="email-icon" />
              <input
                type={showPassword ? "text" : "password"}
                name="password"
                value={formData.password}
                onChange={handleChange}
                placeholder="Enter new password"
                className={`form-input email-input ${errors.password ? 'input-error' : ''}`}
                required
              />
              <button
                type="button"
                className="password-toggle"
                onClick={togglePasswordVisibility}
              >
                {showPassword ? <FaEyeSlash /> : <FaEye />}
              </button>
            </div>
            {errors.password && <p className="error-message">{errors.password}</p>}
          </div>

          <div className="auth-form-input">
            <div className="email-input-container">
              <FaLock className="email-icon" />
              <input
                type={showConfirmPassword ? "text" : "password"}
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleChange}
                placeholder="Confirm new password"
                className={`form-input email-input ${errors.confirmPassword ? 'input-error' : ''}`}
                required
              />
              <button
                type="button"
                className="password-toggle"
                onClick={toggleConfirmPasswordVisibility}
              >
                {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
              </button>
            </div>
            {errors.confirmPassword && <p className="error-message">{errors.confirmPassword}</p>}
          </div>

          <button type="submit" className="signin-button" disabled={isLoading}>
            {isLoading ? "Resetting..." : "Reset Password"}
          </button>

          <p className="signup-link">
            Remember your password? <Link to="/auth">Sign In</Link>
          </p>
        </form>
      </div>
    </div>
  );
};

export default ResetPassword;
