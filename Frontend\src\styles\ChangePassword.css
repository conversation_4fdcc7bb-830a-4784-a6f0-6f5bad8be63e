.ChangePassword {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.ChangePassword__container {
  display: flex;
  width: 100%;
  justify-content: center;
}

.ChangePassword__form-section {
  width: 100%;
  max-width: 500px;
}

.ChangePassword__form {
  display: flex;
  flex-direction: column;
  gap: var(--basefont);
}

.ChangePassword__input-field {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: var(--smallfont);
}

.ChangePassword__input-container {
  display: flex;
  align-items: center;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  background-color: var(--white);
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  overflow: hidden;
  position: relative;
}

.ChangePassword__input-container:focus-within {
  border-color: var(--btn-color);
  box-shadow: 0 0 0 2px rgba(238, 52, 37, 0.1);
}

.ChangePassword__input-container--error {
  border-color: var(--error-color, #dc3545);
}

.ChangePassword__input-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 var(--smallfont);
  color: var(--dark-gray);
  min-width: 40px;
}

.ChangePassword__input {
  width: 100%;
  padding: var(--smallfont) var(--basefont);
  border: none;
  font-size: var(--basefont);
  color: var(--text-color);
  background-color: transparent;
  outline: none;
  padding-right: 50px; /* Make room for password toggle */
}

.ChangePassword__input::placeholder {
  color: var(--dark-gray);
  opacity: 0.7;
}

.ChangePassword__input--error {
  color: var(--error-color, #dc3545);
}

.ChangePassword__password-toggle {
  position: absolute;
  right: var(--smallfont);
  background: none;
  border: none;
  color: var(--dark-gray);
  cursor: pointer;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.3s ease;
}

.ChangePassword__password-toggle:hover {
  color: var(--btn-color);
}

.ChangePassword__password-toggle:focus {
  outline: none;
  color: var(--btn-color);
}

.ChangePassword__error-message {
  color: var(--error-color, #dc3545);
  font-size: var(--smallfont);
  margin-top: 4px;
  margin-bottom: 0;
}

.ChangePassword__form-hint {
  color: var(--dark-gray);
  font-size: var(--smallfont);
  margin-top: 4px;
  margin-bottom: 0;
  opacity: 0.8;
}

.ChangePassword__form-actions {
  display: flex;
  justify-content: flex-start;
  margin-top: var(--basefont);
}

.ChangePassword__submit-btn {
  background-color: var(--btn-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  padding: var(--smallfont) var(--heading5);
  font-size: var(--basefont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 160px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.ChangePassword__submit-btn:hover:not(:disabled) {
  background-color: var(--btn-hover-color, #d63c2a);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(238, 52, 37, 0.2);
}

.ChangePassword__submit-btn:disabled {
  background-color: var(--dark-gray);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.ChangePassword__submit-btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(238, 52, 37, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .ChangePassword__form-section {
    max-width: 100%;
  }
  
  .ChangePassword__input {
    padding: var(--smallfont);
    font-size: var(--smallfont);
  }
  
  .ChangePassword__submit-btn {
    width: 100%;
    padding: var(--basefont);
  }
}

/* Animation for error states */
.ChangePassword__input-container--error {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Loading state for submit button */
.ChangePassword__submit-btn:disabled .fa-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
