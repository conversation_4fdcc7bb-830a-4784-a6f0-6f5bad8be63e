import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { changePassword, reset } from "../../redux/slices/authSlice";
import toast from "../../utils/toast";
import { <PERSON>a<PERSON>ock, <PERSON>a<PERSON>ye, FaEyeSlash } from "react-icons/fa";
import "../../styles/ChangePassword.css";

const ChangePassword = ({ className = "" }) => {
  const dispatch = useDispatch();
  const { isLoading } = useSelector((state) => state.auth);

  const [formData, setFormData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });
  const [errors, setErrors] = useState({});
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null,
      });
    }
  };

  const togglePasswordVisibility = (field) => {
    setShowPasswords({
      ...showPasswords,
      [field]: !showPasswords[field],
    });
  };

  const validateForm = () => {
    const newErrors = {};

    // Validate current password
    if (!formData.currentPassword) {
      newErrors.currentPassword = "Current password is required";
    }

    // Validate new password
    if (!formData.newPassword) {
      newErrors.newPassword = "New password is required";
    } else if (formData.newPassword.length < 6) {
      newErrors.newPassword = "Password must be at least 6 characters";
    } else if (formData.newPassword === formData.currentPassword) {
      newErrors.newPassword = "New password must be different from current password";
    }

    // Validate confirm password
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = "Please confirm your new password";
    } else if (formData.newPassword !== formData.confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
    }

    return newErrors;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    // Reset any previous errors
    dispatch(reset());

    try {
      await dispatch(changePassword({
        currentPassword: formData.currentPassword,
        newPassword: formData.newPassword
      })).unwrap();

      // Show success message
      toast.success("Password changed successfully!");

      // Reset form
      setFormData({
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      });
      setErrors({});
    } catch (error) {
      console.error("Change password error:", error);

      // Extract the error message from the formatted error object
      let errorMessage = "Failed to change password. Please try again.";

      if (typeof error === 'string') {
        errorMessage = error;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      // Handle different types of errors
      if (errorMessage.includes("Password is incorrect")) {
        setErrors({ currentPassword: "Current password is incorrect" });
      } else {
        // Show the actual backend error message
        toast.error(errorMessage);
      }
    }
  };

  return (
    <div className={`ChangePassword ${className}`}>
      <div className="ChangePassword__container">
        <div className="ChangePassword__form-section">
          <form onSubmit={handleSubmit} className="ChangePassword__form">
            <div className="ChangePassword__input-field">
              <div className={`ChangePassword__input-container ${errors.currentPassword ? 'ChangePassword__input-container--error' : ''}`}>
                <div className="ChangePassword__input-icon">
                  <FaLock />
                </div>
                <input
                  type={showPasswords.current ? "text" : "password"}
                  id="currentPassword"
                  name="currentPassword"
                  value={formData.currentPassword}
                  onChange={handleChange}
                  placeholder="Enter current password"
                  className="ChangePassword__input"
                  required
                />
                <button
                  type="button"
                  className="ChangePassword__password-toggle"
                  onClick={() => togglePasswordVisibility('current')}
                >
                  {showPasswords.current ? <FaEyeSlash /> : <FaEye />}
                </button>
              </div>
              {errors.currentPassword && <p className="ChangePassword__error-message">{errors.currentPassword}</p>}
            </div>

            <div className="ChangePassword__input-field">
              <div className={`ChangePassword__input-container ${errors.newPassword ? 'ChangePassword__input-container--error' : ''}`}>
                <div className="ChangePassword__input-icon">
                  <FaLock />
                </div>
                <input
                  type={showPasswords.new ? "text" : "password"}
                  id="newPassword"
                  name="newPassword"
                  value={formData.newPassword}
                  onChange={handleChange}
                  placeholder="Enter new password"
                  className="ChangePassword__input"
                  required
                />
                <button
                  type="button"
                  className="ChangePassword__password-toggle"
                  onClick={() => togglePasswordVisibility('new')}
                >
                  {showPasswords.new ? <FaEyeSlash /> : <FaEye />}
                </button>
              </div>
              {errors.newPassword && <p className="ChangePassword__error-message">{errors.newPassword}</p>}
              <p className="ChangePassword__form-hint">Password must be at least 6 characters long</p>
            </div>

            <div className="ChangePassword__input-field">
              <div className={`ChangePassword__input-container ${errors.confirmPassword ? 'ChangePassword__input-container--error' : ''}`}>
                <div className="ChangePassword__input-icon">
                  <FaLock />
                </div>
                <input
                  type={showPasswords.confirm ? "text" : "password"}
                  id="confirmPassword"
                  name="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  placeholder="Confirm new password"
                  className="ChangePassword__input"
                  required
                />
                <button
                  type="button"
                  className="ChangePassword__password-toggle"
                  onClick={() => togglePasswordVisibility('confirm')}
                >
                  {showPasswords.confirm ? <FaEyeSlash /> : <FaEye />}
                </button>
              </div>
              {errors.confirmPassword && <p className="ChangePassword__error-message">{errors.confirmPassword}</p>}
            </div>

            <div className="ChangePassword__form-actions">
              <button
                type="submit"
                className="ChangePassword__submit-btn"
                disabled={isLoading}
              >
                {isLoading ? "Changing Password..." : "Change Password"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ChangePassword;
