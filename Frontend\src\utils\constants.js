// API Base URL
export const API_BASE_URL =
  import.meta.env.VITE_API_BASE_URL ||
  (import.meta.env.DEV ? "/api" : "http://localhost:5000/api");

// Image Base URL
export const IMAGE_BASE_URL =
  import.meta.env.VITE_IMAGE_BASE_URL || "http://localhost:5000";

// App Configuration
export const APP_CONFIG = {
  APP_NAME: import.meta.env.VITE_APP_NAME || "XO Sports Hub",
  APP_VERSION: import.meta.env.VITE_APP_VERSION || "1.0.0",
};

// Auth endpoints
export const AUTH_ENDPOINTS = {
  REGISTER: "/auth/register",
  LOGIN: "/auth/login",
  FORGOT_PASSWORD: "/auth/forgot-password",
  RESET_PASSWORD: "/auth/reset-password",
  UPDATE_PASSWORD: "/auth/update-password",
  LOGOUT: "/auth/logout",
  ME: "/auth/me",
  UPDATE: "/auth/update",
  VERIFY_EMAIL: "/auth/verify-email",
  GOOGLE_AUTH: "/auth/google",
  GOOGLE_SIGNUP: "/auth/google-signup",
  TOGGLE_ROLE: "/auth/toggle-role",
  COMPLETE_STRIPE_ONBOARDING: "/auth/complete-stripe-onboarding",
};

// Firebase Configuration
export const FIREBASE_CONFIG = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
};

// User endpoints
export const USER_ENDPOINTS = {
  ALL: "/users",
  SINGLE: (id) => `/users/${id}`,
  PROFILE: (id) => `/users/profile/${id}`,
  SELLER: (id) => `/users/sellers/${id}`,
  VERIFY_SELLER: (id) => `/users/verify-seller/${id}`,

  // Seller onboarding endpoints
  SELLER_ONBOARDING: "/users/seller/onboarding",
  COMPLETE_ONBOARDING: "/users/seller/complete-onboarding",
};

// Content endpoints
export const CONTENT_ENDPOINTS = {
  BASE: "/content",
  ALL: "/content",
  CATEGORIES: "/content/categories",
  TRENDING: "/content/trending",
  SINGLE: (id) => `/content/${id}`,
  SELLER_CONTENT: "/content/seller/me",
  UPLOAD: "/content/upload",
  CONTENT_REVIEWS: (contentId) => `/reviews/content/${contentId}`,
  CREATE_REVIEW: '/reviews',
};

// Order endpoints
export const ORDER_ENDPOINTS = {
  ALL: "/orders",
  SINGLE: (id) => `/orders/${id}`,
  BUYER_ORDERS: "/orders/buyer",
  BUYER_DOWNLOADS: "/orders/buyer/downloads",
  SELLER_ORDERS: "/orders/seller",
  DOWNLOAD: (id) => `/orders/${id}/download`,
  STREAM: (id) => `/orders/${id}/stream`,
};

// Validation utilities
export const VALIDATION = {
  // MongoDB ObjectId validation (24 hex characters)
  isValidObjectId: (id) => {
    if (!id || typeof id !== 'string') return false;
    return /^[0-9a-fA-F]{24}$/.test(id.trim());
  },

  // Check if ID is undefined or invalid
  isValidId: (id) => {
    return id && id !== 'undefined' && id.trim() !== '' && VALIDATION.isValidObjectId(id);
  }
};

// Bid endpoints
export const BID_ENDPOINTS = {
  BASE: "/bids",
  ALL: "/bids",
  SINGLE: (id) => `/bids/${id}`,
  CANCEL: (id) => `/bids/${id}/cancel`,
  CONTENT_BIDS: (contentId) => `/bids/content/${contentId}`,
  USER_BIDS: "/bids/user",
  SELLER_BIDS: "/bids/seller",
  SELLER_CONTENT_BIDS: (contentId) => `/bids/seller/content/${contentId}`,
  END_AUCTION: (contentId) => `/bids/end-auction/${contentId}`,
  UPDATE_STATUS: (id) => `/bids/${id}/status`,
};

// Request endpoints
export const REQUEST_ENDPOINTS = {
  ALL: "/requests",
  SINGLE: (id) => `/requests/${id}`,
  RESPOND: (id) => `/requests/${id}/respond`,
  CANCEL: (id) => `/requests/${id}/cancel`,
  SUBMIT: (id) => `/requests/${id}/submit`,
  BUYER_REQUESTS: "/requests/buyer",
  SELLER_REQUESTS: "/requests/seller",
};

// Payment endpoints
export const PAYMENT_ENDPOINTS = {
  ALL: "/payments",
  SINGLE: (id) => `/payments/${id}`,
  CREATE_INTENT: "/payments/create-intent",
  CONFIRM: "/payments/confirm",
  BUYER_PAYMENTS: "/payments/buyer",
  SELLER_PAYMENTS: "/payments/seller",
  PAYOUT: (id) => `/payments/${id}/payout`,
  CREATE_CONNECT_ACCOUNT: "/payments/create-connect-account",
  CONNECT_ACCOUNT_STATUS: (accountId) => `/payments/connect-account-status/${accountId}`,
  CONNECT_ACCOUNT_DETAILS: (accountId) => `/payments/connect-account-details/${accountId}`,
  UPDATE_CONNECT_ACCOUNT: "/payments/update-connect-account",
  CREATE_DASHBOARD_LINK: "/payments/create-dashboard-link",
};

// Card endpoints
export const CARD_ENDPOINTS = {
  ALL: "/cards",
  SINGLE: (id) => `/cards/${id}`,
  UPDATE: (id) => `/cards/${id}`,
  DELETE: (id) => `/cards/${id}`,
};

// Notification endpoints
export const NOTIFICATION_ENDPOINTS = {
  ALL: "/notifications",
  USER_NOTIFICATIONS: "/notifications/me",
  READ: (id) => `/notifications/${id}/read`,
  READ_ALL: "/notifications/read-all",
  UNREAD_COUNT: "/notifications/unread-count",
};

// CMS endpoints
export const CMS_ENDPOINTS = {
  ALL: "/cms",
  PUBLISHED: "/cms/published",
  SINGLE: (slug) => `/cms/${slug}`,
  CONTACT: "/cms/contact",
};

// Settings endpoints
export const SETTINGS_ENDPOINTS = {
  ALL: "/settings",
  PUBLIC: "/settings/public",
  SINGLE: (id) => `/settings/${id}`,
};

// Dashboard endpoints
export const DASHBOARD_ENDPOINTS = {
  STATS: "/dashboard/stats",
  USERS: "/dashboard/users",
  CONTENT: "/dashboard/content",
  ORDERS: "/dashboard/orders",
  REVENUE: "/dashboard/revenue",
  ACTIVITY: "/dashboard/activity",
};

// Review endpoints
export const REVIEW_ENDPOINTS = {
  ALL: "/reviews",
  SINGLE: (id) => `/reviews/${id}`,
  CONTENT_REVIEWS: (contentId) => `/reviews/content/${contentId}`,
  SELLER_REVIEWS: (sellerId) => `/reviews/seller/${sellerId}`,
};

// Message endpoints
export const MESSAGE_ENDPOINTS = {
  CONVERSATIONS: "/messages/conversations",
  CONVERSATION: (id) => `/messages/conversations/${id}`,
  ARCHIVE: (id) => `/messages/conversations/${id}/archive`,
  MESSAGES: (conversationId) =>
    `/messages/conversations/${conversationId}/messages`,
  READ: (conversationId) => `/messages/conversations/${conversationId}/read`,
  UNREAD_COUNT: "/messages/unread-count",
};

// Wishlist endpoints
export const WISHLIST_ENDPOINTS = {
  ALL: "/wishlist",
  REMOVE: (contentId) => `/wishlist/${contentId}`,
  CHECK: (contentId) => `/wishlist/check/${contentId}`,
};

// Local storage keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: "xosportshub_token",
  USER: "xosportshub_user",
  STEP: "seller_onboarding_step",
  FORM_DATA: "seller_onboarding_data",

};

// HTTP status codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  SERVER_ERROR: 500,
};

// User roles
export const USER_ROLES = {
  ADMIN: "admin",
  SELLER: "seller",
  BUYER: "buyer",
};

// Utility function to get full image URL
export const getImageUrl = (imagePath) => {
  if (!imagePath) return null;

  // If it's already a full URL (S3 or external), return as is
  if (imagePath.startsWith("http://") || imagePath.startsWith("https://")) {
    return imagePath;
  }

  // If it's a relative path, prepend the image base URL
  if (imagePath.startsWith("/uploads/")) {
    return `${IMAGE_BASE_URL}${imagePath}`;
  }

  // If it's just a filename, prepend the full uploads path
  return `${IMAGE_BASE_URL}/uploads/${imagePath}`;
};

// Enhanced utility function to intelligently handle both S3 and local URLs
export const getFileUrl = (filePath) => {
  if (!filePath) return null;

  // If it's already a full URL (S3 or external), return as is
  if (filePath.startsWith("http://") || filePath.startsWith("https://")) {
    return filePath;
  }

  // If it's a relative path, prepend the image base URL
  if (filePath.startsWith("/uploads/")) {
    return `${IMAGE_BASE_URL}${filePath}`;
  }

  // If it's just a filename, prepend the full uploads path
  return `${IMAGE_BASE_URL}/uploads/${filePath}`;
};

// Utility to check if a URL is an S3 URL
export const isS3Url = (url) => {
  if (!url) return false;
  return url.includes('amazonaws.com') ||
    url.includes('s3.') ||
    (url.startsWith('https://') && url.includes('.s3.'));
};

// Extract S3 key from S3 URL
export const extractS3Key = (url) => {
  if (!url || typeof url !== 'string') return null;

  try {
    const urlObj = new URL(url);
    const pathParts = urlObj.pathname.split('/').filter(part => part.length > 0);

    if (urlObj.hostname.includes('.s3.') || urlObj.hostname.includes('.s3-')) {
      // Format: https://bucket.s3.region.amazonaws.com/key
      return pathParts.join('/');
    } else if (urlObj.hostname.startsWith('s3.')) {
      // Format: https://s3.region.amazonaws.com/bucket/key
      return pathParts.slice(1).join('/'); // Remove bucket name
    }

    return null;
  } catch (error) {
    console.error('Error extracting S3 key from URL:', error);
    return null;
  }
};

// Smart URL resolver that handles both S3 and local files with fallback
export const resolveFileUrl = (filePath, options = {}) => {
  const { forceLocal = false } = options;

  if (!filePath) return null;

  // If forcing local or if it's already a local path
  if (forceLocal || filePath.startsWith('/uploads/')) {
    return `${IMAGE_BASE_URL}${filePath.startsWith('/') ? filePath : `/uploads/${filePath}`}`;
  }

  // If it's already a full URL (S3 or external), return as is
  if (filePath.startsWith("http://") || filePath.startsWith("https://")) {
    return filePath;
  }

  // Default to local path construction
  return `${IMAGE_BASE_URL}/uploads/${filePath}`;
};

// Smart file URL with error handling and fallback
export const getSmartFileUrl = (filePath, options = {}) => {
  const { enableFallback = true } = options;

  if (!filePath) return null;

  // If it's already a full URL (S3 or external), return as is
  if (filePath.startsWith("http://") || filePath.startsWith("https://")) {
    return filePath;
  }

  // For relative paths, construct local URL
  if (filePath.startsWith("/uploads/")) {
    return `${IMAGE_BASE_URL}${filePath}`;
  }

  // Default case - construct local URL
  return `${IMAGE_BASE_URL}/uploads/${filePath}`;
};

// Convert S3 path to local path for fallback
export const getLocalFallbackUrl = (s3OrLocalPath) => {
  if (!s3OrLocalPath) return null;

  // If it's already a local path, return as is
  if (s3OrLocalPath.startsWith('/uploads/') || !s3OrLocalPath.includes('http')) {
    return getFileUrl(s3OrLocalPath);
  }

  // If it's an S3 URL, extract the filename and create local URL
  if (isS3Url(s3OrLocalPath)) {
    try {
      const url = new URL(s3OrLocalPath);
      const pathParts = url.pathname.split('/');
      const filename = pathParts[pathParts.length - 1];

      // Try to preserve the folder structure if possible
      if (pathParts.includes('uploads')) {
        const uploadsIndex = pathParts.indexOf('uploads');
        const relativePath = pathParts.slice(uploadsIndex).join('/');
        return `${IMAGE_BASE_URL}/${relativePath}`;
      }

      // Fallback to just the filename in uploads folder
      return `${IMAGE_BASE_URL}/uploads/${filename}`;
    } catch (error) {
      console.warn('Failed to parse S3 URL for fallback:', s3OrLocalPath);
      return null;
    }
  }

  // For other URLs, return as is
  return s3OrLocalPath;
};

// Generate proxy URL for content access (replaces signed URLs)
export const getProxyContentUrl = (contentId) => {
  if (!contentId) return null;
  return `${API_BASE_URL}/proxy/content/${contentId}`;
};

// Generate proxy URL for preview access
export const getProxyPreviewUrl = (contentId) => {
  if (!contentId) return null;
  return `${API_BASE_URL}/proxy/preview/${contentId}`;
};

// Generate proxy URL for streaming (replaces signed URLs for videos)
export const getProxyStreamUrl = (contentId) => {
  if (!contentId) return null;
  return `${API_BASE_URL}/proxy/stream/${contentId}`;
};

// Generate proxy URL for thumbnails
export const getProxyThumbnailUrl = (contentId) => {
  if (!contentId) return null;
  return `${API_BASE_URL}/proxy/thumbnail/${contentId}`;
};

// Generate public URL for thumbnails (no authentication required)
export const getPublicThumbnailUrl = (contentId) => {
  if (!contentId) return null;
  return `${API_BASE_URL}/content/thumbnail/${contentId}`;
};

// Helper function to get proxy URL with authentication token
export const getProxyUrlWithAuth = (baseUrl) => {
  const token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
  if (!token) return baseUrl;

  const url = new URL(baseUrl);
  url.searchParams.set('token', token);
  return url.toString();
};

// Helper function to get authentication headers for fetch requests
export const getAuthHeaders = () => {
  const token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
  return token ? { 'Authorization': `Bearer ${token}` } : {};
};

// Legacy function - kept for backward compatibility during migration
// TODO: Remove after all components are updated to use proxy URLs
export const getSignedFileUrl = async (filePath) => {
  if (!filePath) return null;

  try {
    // For S3 URLs, we'll need to request signed URLs from the backend
    if (isS3Url(filePath)) {
      // Extract S3 key from the URL
      const s3Key = extractS3Key(filePath);
      if (s3Key) {
        const token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
        if (token) {
          const response = await fetch(`${API_BASE_URL}/content/file/${encodeURIComponent(s3Key)}`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });

          if (response.ok) {
            const data = await response.json();
            return data.data?.signedUrl || filePath;
          }
        }
      }
      return filePath;
    } else {
      // For local files, use smart file URL
      return getSmartFileUrl(filePath);
    }
  } catch (error) {
    console.error('Error getting file URL:', error);
    // Fallback to smart file URL for local files
    if (!isS3Url(filePath)) {
      return getSmartFileUrl(filePath);
    }
    return filePath;
  }
};

// Legacy function - kept for backward compatibility during migration
// TODO: Remove after all components are updated to use proxy URLs
export const getSignedPreviewUrl = async (filePath) => {
  if (!filePath) return null;

  // If it's not an S3 URL, use the regular smart file URL
  if (!isS3Url(filePath)) {
    return getSmartFileUrl(filePath);
  }

  // For S3 URLs, use the same logic as getSignedFileUrl
  return getSignedFileUrl(filePath);
};

// Legacy function - kept for backward compatibility during migration
// TODO: Remove after all components are updated to use proxy URLs
export const getVideoStreamUrl = (filePath) => {
  if (!filePath) return null;

  // If it's not an S3 URL, use the regular smart file URL
  if (!isS3Url(filePath)) {
    return getSmartFileUrl(filePath);
  }

  try {
    // Extract S3 key from the URL
    const url = new URL(filePath);
    const pathParts = url.pathname.split('/').filter(part => part.length > 0);
    let s3Key;

    if (url.hostname.includes('.s3.') || url.hostname.includes('.s3-')) {
      // Format: https://bucket.s3.region.amazonaws.com/key
      s3Key = pathParts.join('/');
    } else if (url.hostname.startsWith('s3.')) {
      // Format: https://s3.region.amazonaws.com/bucket/key
      s3Key = pathParts.slice(1).join('/'); // Remove bucket name
    } else {
      // Fallback to original URL
      return filePath;
    }

    if (!s3Key) {
      console.warn('Could not extract S3 key from URL for streaming:', filePath);
      return filePath;
    }

    // Return streaming endpoint URL
    return `${API_BASE_URL}/content/stream/${encodeURIComponent(s3Key)}`;
  } catch (error) {
    console.error('Error generating streaming URL:', error);
    return filePath;
  }
};

// Generate a placeholder image as base64 SVG
export const getPlaceholderImage = (width = 200, height = 120, text = "No Image") => {
  const svg = `
    <svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="${width}" height="${height}" fill="#F3F4F6"/>
      <rect x="20" y="20" width="${width - 40}" height="${height - 40}" fill="#E5E7EB" rx="8"/>
      <g transform="translate(${width / 2 - 24}, ${height / 2 - 24})">
        <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
          <path d="M21 19V5C21 3.9 20.1 3 19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19ZM8.5 13.5L11 16.51L14.5 12L19 18H5L8.5 13.5Z" fill="#9CA3AF"/>
        </svg>
      </g>
      <text x="${width / 2}" y="${height - 15}" text-anchor="middle" fill="#6B7280" font-family="Arial, sans-serif" font-size="12">${text}</text>
    </svg>
  `;

  return `data:image/svg+xml;base64,${btoa(svg)}`;
};


