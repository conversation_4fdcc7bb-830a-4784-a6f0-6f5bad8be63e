import React from "react";
import SellerLayout from "../../components/seller/SellerLayout";
import ChangePassword from "../../components/common/ChangePassword";
import { FaLock } from "react-icons/fa";
import "../../styles/SellerChangePassword.css";

const SellerChangePassword = () => {
  return (
    <SellerLayout>
      <div className="seller-change-password-container">
        <div className="seller-change-password-content">
          <div className="seller-change-password-header">
            <FaLock className="seller-change-password-icon" />
            <h1>Change Password</h1>
          </div>

          <div className="seller-change-password-form">
            <ChangePassword />
          </div>
        </div>
      </div>
    </SellerLayout>
  );
};

export default SellerChangePassword;
